# Driver and Vehicle Verification API Documentation

## Overview

This documentation provides comprehensive details for the Driver Verification and Vehicle Verification API endpoints, including JSON request bodies, response formats, validation rules, and status codes.

## Base URL
```
{BASE_URL}/api/driver/
```

## Authentication
All endpoints require Bearer token authentication:
```
Authorization: Bearer {access_token}
```

---

## Driver Verification

### 1. Get Driver Verification Form

**Endpoint:** `GET /driver-verification`

**Description:** Retrieves the driver verification form structure and current verification status.

#### Response Format

**Success Response (200):**
```json
{
  "remark": "driver_form|under_review|already_verified",
  "status": "success",
  "message": ["Driver verification field is below"],
  "data": {
    "form": {
      "field_name": {
        "name": "Field Display Name",
        "label": "field_name",
        "is_required": "required|optional",
        "instruction": "Field instruction text",
        "extensions": "jpg,jpeg,png,pdf",
        "options": ["option1", "option2"],
        "type": "text|select|radio|textarea|checkbox|file|email|url|number|datetime|date|time",
        "width": "12|6|4|3"
      }
    },
    "driver_data": [
      {
        "name": "Field Name",
        "type": "text",
        "value": "submitted_value"
      }
    ],
    "file_path": "/assets/images/verify/"
  }
}
```

**Status Variations:**
- `driver_form`: Initial form request
- `under_review`: Verification pending
- `already_verified`: Already verified

### 2. Submit Driver Verification

**Endpoint:** `POST /driver-verification-store`

**Description:** Submits driver verification data including documents and personal information.

#### Request Format

**Content-Type:** `multipart/form-data`

```json
{
  "field_name": "field_value"
}
```

**Example Request:**
```json
{
  "license_number": "DL123456789",
  "license_expire": "2025-12-31",
  "license_photo": "Screenshot (216).png",
  "nid_image_both_side": "Screenshot (216).png",
  "driving_experience": "5 years",
  "selfie": "Screenshot (16).png",
  "nid_number": "564"
}
```

#### Validation Rules

- **Dynamic fields:** Based on form configuration
- **File uploads:** 
  - Supported extensions: jpg, jpeg, png, pdf, doc, docx
  - Max file size: As configured in system
- **identification_document_number:** Required string, max 100 characters
- **selfie:** Required image file (jpg, jpeg, png)

#### Response Format

**Success Response (200):**
```json
{
    "remark": "under_review",
    "status": "success",
    "message": [
        "We are currently reviewing your driver information."
    ],
    "data": {
        "driver_data": [
            {
                "name": "License Number",
                "type": "text",
                "value": "DL123456789"
            },
            {
                "name": "License Expire",
                "type": "date",
                "value": "2025-12-31"
            },
            {
                "name": "License Photo",
                "type": "file",
                "value": "2025/06/27/685eb528203be1751037224.png"
            },
            {
                "name": "NID Image_Both SIde",
                "type": "file",
                "value": "2025/06/27/685eb5284a5281751037224.png"
            },
            {
                "name": "NID Number",
                "type": "number",
                "value": null
            },
            {
                "name": "Driving Experience",
                "type": "text",
                "value": "5 years"
            },
            {
                "name": "selfie",
                "type": "file",
                "value": "2025/06/27/685eb52870ccd1751037224.png"
            }
        ],
        "file_path": "assets/verify"
    }
}
```

**Error Response (422):**
```json
{
  "remark": "validation_error",
  "status": "error",
  "message": [
    "The full name field is required.",
    "The driving license field is required."
  ]
}
```

**Already Submitted Response (200):**
```json
{
  "remark": "under_review|already_verified",
  "status": "success|error",
  "message": ["We are currently reviewing your driver information."],
  "data": {
    "driver_data": [...],
    "file_path": "/assets/images/verify/"
  }
}
```

---

## Vehicle Verification

### 1. Get Vehicle Verification Form

**Endpoint:** `GET /vehicle-verification`

**Description:** Retrieves the vehicle verification form structure, services, brands, and current verification status.

#### Response Format

**Success Response (200):**
```json
{
  "remark": "vehicle_form|under_review|already_verified",
  "status": "success",
  "message": ["Vehicle verification field is below"],
  "data": {
    "form": {
      "field_name": {
        "name": "Field Display Name",
        "label": "field_name",
        "is_required": "required|optional",
        "instruction": "Field instruction text",
        "extensions": "jpg,jpeg,png,pdf",
        "options": [],
        "type": "text|select|file",
        "width": "12|6|4|3"
      }
    },
    "services": [
      {
        "id": 1,
        "name": "Economy",
        "image": "economy.png",
        "status": 1
      }
    ],
    "brands": [
      {
        "id": 1,
        "name": "Toyota",
        "image": "toyota.png",
        "status": 1
      }
    ],
    "rider_rules": [
      {
        "id": 1,
        "name": "No Smoking",
        "status": 1
      }
    ],
    "vehicle_data": [
      {
        "name": "Vehicle Model",
        "type": "text",
        "value": "Camry"
      }
    ],
    "vehicle_video": "vehicle_video.mp4",
    "video_description": "Video description text",
    "service": {
      "id": 1,
      "name": "Economy"
    },
    "brand": {
      "id": 1,
      "name": "Toyota"
    },
    "file_path": "/assets/images/verify/",
    "video_path": "/assets/images/driver_video/",
    "service_image_path": "/assets/images/service/",
    "brand_image_path": "/assets/images/brand/"
  }
}
```

### 2. Submit Vehicle Verification

**Endpoint:** `POST /vehicle-verification-store`

**Description:** Submits vehicle verification data including documents, service selection, and optional video.

#### Request Format

**Content-Type:** `multipart/form-data`

```json
{
  "service_id": 1,
  "brand_id": 1,
  "rules": [1, 2, 3],
  "vehicle_video": "vehicle_video_file.mp4",
  "video_description": "Description of the vehicle video",
  "field_name": "field_value"
}
```

**Example Request:**
```json
{
  "vehicle_model": "Toyota Camry",
  "vehicle_color": "Black",
  "vehicle_year": 2020,
  "vehicle_document": "driver combine rides body.pdf",
  "vehicle_image": "Screenshot (216).png",
  "service_id": 1,
  "brand_id": 1,
  "rules": ["no eating", "no smoking", "no drinking"],
  "vehicle_video": "Picture in picture 2025-03-15 22-44-17.mp4",
  "video_description": "Complete walkthrough of the vehicle showing exterior, interior, and engine"
}
```

#### Validation Rules

- **service_id:** Required integer, must exist in services table
- **brand_id:** Required integer, must exist in brands table
- **rules:** Optional array of integers, each must exist in rider_rules table
- **vehicle_video:** Optional file, supported formats: mp4, mov, 3gp, avi
- **video_description:** Optional string, max 255 characters
- **Dynamic fields:** Based on form configuration
- **File uploads:** Supported extensions as configured

#### Response Format

**Success Response (200):**
```json
{
  "remark": "verification_submitted",
  "status": "success",
  "message": ["Vehicle verification data submitted successfully"],
  "data": {
    "vehicle_data": [
      {
        "name": "Vehicle Model",
        "type": "text",
        "value": "Camry 2020"
      }
    ],
    "service": {
      "id": 1,
      "name": "Economy"
    },
    "brand": {
      "id": 2,
      "name": "Toyota"
    },
    "file_path": "/assets/images/verify/",
    "video_path": "/assets/images/driver_video/"
  }
}
```

**Error Response (422):**
```json
{
  "remark": "validation_error",
  "status": "error",
  "message": [
    "The service id field is required.",
    "The brand id field is required.",
    "The vehicle model field is required."
  ]
}
```

**Service/Brand Not Found (404):**
```json
{
  "remark": "not_found",
  "status": "error",
  "message": ["Service currently unavailable"]
}
```

**Already Submitted Response (200):**
```json
{
  "remark": "already_submit|verified",
  "status": "error",
  "message": ["Vehicle information already submitted"]
}
```

---

## Status Constants

### Verification Status
- `0` - UNVERIFIED
- `1` - VERIFIED  
- `2` - PENDING
- `9` - VERIFICATION_REJECT

### Driver Model Fields
- `dv` - Driver verification status
- `vv` - Vehicle verification status
- `driver_data` - JSON object containing driver verification data
- `vehicle_data` - JSON object containing vehicle verification data
- `vehicle_verification_video` - Video filename
- `vehicle_verification_description` - Video description

---

## File Upload Guidelines

### Supported File Types
- **Images:** jpg, jpeg, png, gif
- **Documents:** pdf, doc, docx, txt
- **Videos:** mp4, mov, 3gp, avi
- **Others:** xlsx, csv, rtf, wav, mp3

### File Paths
- **Verification documents:** `/assets/images/verify/YYYY/MM/DD/`
- **Driver videos:** `/assets/images/driver_video/`
- **Service images:** `/assets/images/service/`
- **Brand images:** `/assets/images/brand/`

### File Size Limits
File size limits are configured in the system settings and enforced during upload.

---

## Error Handling

### Common Error Responses

**Validation Error (422):**
```json
{
  "remark": "validation_error",
  "status": "error",
  "message": ["Array of validation error messages"]
}
```

**Authentication Error (401):**
```json
{
  "remark": "unauthenticated",
  "status": "error",
  "message": ["Authentication required"]
}
```

**Server Error (500):**
```json
{
  "remark": "exception",
  "status": "error",
  "message": ["An error occurred while processing your request"]
}
```

---

## Integration Notes

1. **Form Structure:** The form fields are dynamically generated and can be customized by administrators
2. **File Uploads:** Use multipart/form-data for requests containing file uploads
3. **Status Tracking:** Monitor verification status through the `dv` and `vv` fields
4. **Admin Notifications:** Successful submissions trigger admin notifications for review
5. **Video Integration:** Vehicle videos are stored separately and integrated into the verification process

This documentation provides complete coverage of the driver and vehicle verification API endpoints with detailed request/response examples for easy integration.
