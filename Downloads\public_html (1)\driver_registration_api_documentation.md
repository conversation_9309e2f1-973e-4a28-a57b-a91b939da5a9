# Driver Registration API Documentation

## Overview

The Driver Registration API allows new drivers to register for the ride-sharing platform. The registration process focuses on basic account creation, with identification document verification handled separately during the driver verification process.

## Base URL

```
https://your-domain.com/api/driver
```

## Authentication

Driver registration does not require authentication, but subsequent API calls will require a Bearer token.

## Driver Registration Endpoint

### Register New Driver

Creates a new driver account. Identification document verification is handled separately during the driver verification process.

**Endpoint:** `POST /register`

**Authentication Required:** No

**Request Headers:**
```
Content-Type: application/json
Accept: application/json
```

**Request Body:**

```json
{
  "firstname": "<PERSON>",
  "lastname": "Doe",
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "password_confirmation": "SecurePassword123!",
  "agree": 1
}
```

**Request Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| firstname | String | Yes | Driver's first name |
| lastname | String | Yes | Driver's last name |
| email | String | Yes | Driver's email address (must be unique) |
| password | String | Yes | Driver's password (must meet security requirements) |
| password_confirmation | String | Yes | Password confirmation (must match password) |
| agree | Integer | Conditional | Agreement to terms (required if terms agreement is enabled) |

**Password Requirements:**
- Minimum 6 characters
- If secure password is enabled in settings:
  - Must contain uppercase and lowercase letters
  - Must contain numbers
  - Must contain symbols
  - Must not be compromised

**Success Response:**

```json
{
  "status": "success",
  "message": "success",
  "data": {
    "access_token": "1|abcdef123456789...",
    "driver": {
      "id": 1,
      "firstname": "John",
      "lastname": "Doe",
      "email": "<EMAIL>",
      "username": null,
      "mobile": null,
      "country_code": null,
      "dial_code": null,
      "country_name": null,
      "image": null,
      "status": 1,
      "ev": 0,
      "sv": 0,
      "dv": 0,
      "vv": 0,
      "ts": 0,
      "tv": 1,
      "profile_complete": 0,
      "balance": "0.00000000",
      "total_reviews": 0,
      "avg_rating": "0.00",
      "online_status": true,
      "created_at": "2023-12-27T10:30:00.000000Z",
      "updated_at": "2023-12-27T10:30:00.000000Z"
    },
    "token_type": "Bearer",
    "image_path": "/assets/images/driver/"
  },
  "notification": ["Registration successful"]
}
```

**Error Responses:**

**Registration Disabled:**
```json
{
  "status": "error",
  "message": "error",
  "notification": ["Registration not allowed"]
}
```

**Validation Errors:**
```json
{
  "status": "error",
  "message": "error",
  "notification": [
    "The first name field is required",
    "The email has already been taken",
    "The identification document number field is required"
  ]
}
```

**Specific Validation Rules:**

| Field | Validation Rules |
|-------|------------------|
| firstname | Required |
| lastname | Required |
| email | Required, valid email format, unique in drivers table |
| password | Required, confirmed, meets password policy |
| agree | Required if terms agreement is enabled in settings |

## Post-Registration Process

After successful registration, drivers will need to complete additional steps:

1. **Email Verification** (if enabled)
2. **Mobile Verification** (if enabled)
3. **Document Verification** - Submit identification document number and upload verification documents
4. **Vehicle Verification** - Provide vehicle information and documents
5. **Profile Completion** - Complete additional profile information

## Driver Status Fields

| Field | Description | Values |
|-------|-------------|---------|
| ev | Email Verification | 0=Unverified, 1=Verified |
| sv | SMS/Mobile Verification | 0=Unverified, 1=Verified |
| dv | Document Verification | 0=Unverified, 1=Verified, 2=Pending |
| vv | Vehicle Verification | 0=Unverified, 1=Verified, 2=Pending |
| ts | Two-Factor Authentication | 0=Disabled, 1=Enabled |
| tv | Phone Verification | 0=Unverified, 1=Verified |
| status | Account Status | 0=Banned, 1=Active |
| profile_complete | Profile Completion | 0=Incomplete, 1=Complete |

## Security Features

1. **Password Security**: Configurable password complexity requirements
2. **Email Uniqueness**: Prevents duplicate registrations
3. **Terms Agreement**: Optional terms and conditions acceptance
4. **Account Status Tracking**: Multiple verification levels
5. **Identification Document Verification**: Handled during the driver verification process for enhanced security

## Admin Features

Administrators can:
- View driver identification document numbers in the admin panel
- Edit driver identification document numbers
- Track driver verification status
- Manage driver accounts and permissions

## Next Steps After Registration

1. **Complete Profile**: Add mobile number, address, and other details
2. **Document Verification**: Submit identification document number and upload verification documents
3. **Vehicle Registration**: Add vehicle information and documents
4. **Account Activation**: Wait for admin approval if required

## Error Handling

The API returns appropriate HTTP status codes:
- `200 OK`: Successful registration
- `400 Bad Request`: Validation errors
- `403 Forbidden`: Registration disabled
- `422 Unprocessable Entity`: Validation failures

## Rate Limiting

Registration endpoints may be subject to rate limiting to prevent abuse. Contact your system administrator for specific limits.

---

This documentation covers the driver registration process. The identification document number is now collected during the driver verification process for enhanced security. For additional API endpoints or support, please refer to the main API documentation or contact the system administrator.
