@php
    $services = App\Models\Service::active()->orderBy('name')->get();
@endphp
<form id="filter-form">
    <div class="form-group">
        <label class="form-label">@lang('Service Type')</label>
        <select class="form-select select2" name="service_id" data-minimum-results-for-search="-1">
            <option value="">@lang('All Services')</option>
            @foreach($services as $service)
                <option value="{{ $service->id }}" @selected(request()->service_id == $service->id)>
                    {{ __($service->name) }}
                </option>
            @endforeach
        </select>
    </div>
    <x-admin.other.filter_date />
    <x-admin.other.order_by />
    <x-admin.other.per_page_record />
    <x-admin.other.filter_dropdown_btn />
</form>
